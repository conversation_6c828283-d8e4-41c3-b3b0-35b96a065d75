{"name": "delivery-tracker-microservice", "version": "1.0.0", "description": "Real-time delivery tracking microservice for food delivery app", "main": "delivery-tracker.js", "scripts": {"start": "node delivery-tracker.js", "dev": "nodemon delivery-tracker.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "ws": "^8.14.2"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0"}, "keywords": ["delivery", "tracking", "microservice", "websocket", "real-time"], "author": "Food Delivery App", "license": "MIT"}