const express = require('express');
const cors = require('cors');
const WebSocket = require('ws');
const http = require('http');

const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

app.use(cors());
app.use(express.json());

// In-memory storage for demo (use Redis/Database in production)
const orders = new Map();
const deliveryAgents = new Map();

// Mock restaurant and delivery locations
const mockLocations = {
  restaurant: {
    latitude: 28.6139,
    longitude: 77.2090,
    name: "Delicious Bites Restaurant"
  },
  deliveryAreas: [
    { latitude: 28.6289, longitude: 77.2065, address: "Connaught Place, New Delhi" },
    { latitude: 28.5355, longitude: 77.3910, address: "Noida Sector 18" },
    { latitude: 28.4595, longitude: 77.0266, address: "Gurgaon Cyber City" },
  ]
};

// Initialize delivery tracking for an order
app.post('/api/orders/:orderId/start-tracking', (req, res) => {
  const { orderId } = req.params;
  const { deliveryAddress } = req.body;
  
  // Create mock delivery agent
  const agentId = `agent_${Date.now()}`;
  const deliveryAgent = {
    id: agentId,
    name: `Delivery Partner ${Math.floor(Math.random() * 100)}`,
    phone: `+91 ${Math.floor(Math.random() * 9000000000) + 1000000000}`,
    currentLocation: { ...mockLocations.restaurant },
    destination: deliveryAddress || mockLocations.deliveryAreas[0],
    status: 'assigned'
  };
  
  deliveryAgents.set(agentId, deliveryAgent);
  
  const order = {
    id: orderId,
    agentId,
    status: 'preparing',
    estimatedDelivery: new Date(Date.now() + 30 * 60000), // 30 minutes from now
    route: [mockLocations.restaurant, deliveryAgent.destination],
    createdAt: new Date()
  };
  
  orders.set(orderId, order);
  
  // Start simulating movement
  simulateDeliveryMovement(orderId);
  
  res.json({
    success: true,
    order,
    deliveryAgent
  });
});

// Get order tracking info
app.get('/api/orders/:orderId/tracking', (req, res) => {
  const { orderId } = req.params;
  const order = orders.get(orderId);
  
  if (!order) {
    return res.status(404).json({ error: 'Order not found' });
  }
  
  const agent = deliveryAgents.get(order.agentId);
  
  res.json({
    order,
    deliveryAgent: agent,
    restaurant: mockLocations.restaurant
  });
});

// WebSocket connection for real-time updates
wss.on('connection', (ws) => {
  console.log('Client connected to WebSocket');
  
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message);
      
      if (data.type === 'subscribe' && data.orderId) {
        ws.orderId = data.orderId;
        console.log(`Client subscribed to order ${data.orderId}`);
        
        // Send current status
        const order = orders.get(data.orderId);
        if (order) {
          const agent = deliveryAgents.get(order.agentId);
          ws.send(JSON.stringify({
            type: 'location_update',
            orderId: data.orderId,
            order,
            deliveryAgent: agent
          }));
        }
      }
    } catch (error) {
      console.error('WebSocket message error:', error);
    }
  });
  
  ws.on('close', () => {
    console.log('Client disconnected from WebSocket');
  });
});

// Simulate delivery agent movement
function simulateDeliveryMovement(orderId) {
  const order = orders.get(orderId);
  if (!order) return;
  
  const agent = deliveryAgents.get(order.agentId);
  if (!agent) return;
  
  let step = 0;
  const totalSteps = 20; // Number of movement steps
  
  const interval = setInterval(() => {
    if (step >= totalSteps || order.status === 'delivered') {
      clearInterval(interval);
      
      // Mark as delivered
      order.status = 'delivered';
      agent.status = 'delivered';
      agent.currentLocation = agent.destination;
      
      broadcastUpdate(orderId);
      return;
    }
    
    // Calculate intermediate position
    const progress = step / totalSteps;
    const startLat = mockLocations.restaurant.latitude;
    const startLng = mockLocations.restaurant.longitude;
    const endLat = agent.destination.latitude;
    const endLng = agent.destination.longitude;
    
    agent.currentLocation = {
      latitude: startLat + (endLat - startLat) * progress,
      longitude: startLng + (endLng - startLng) * progress
    };
    
    // Update order status based on progress
    if (step === 5) {
      order.status = 'picked_up';
      agent.status = 'picked_up';
    } else if (step === 10) {
      order.status = 'on_the_way';
      agent.status = 'on_the_way';
    }
    
    step++;
    broadcastUpdate(orderId);
  }, 3000); // Update every 3 seconds
}

// Broadcast updates to subscribed clients
function broadcastUpdate(orderId) {
  const order = orders.get(orderId);
  const agent = deliveryAgents.get(order.agentId);
  
  const updateData = {
    type: 'location_update',
    orderId,
    order,
    deliveryAgent: agent,
    timestamp: new Date()
  };
  
  wss.clients.forEach((client) => {
    if (client.readyState === WebSocket.OPEN && client.orderId === orderId) {
      client.send(JSON.stringify(updateData));
    }
  });
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date() });
});

const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`🚀 Delivery Tracking Microservice running on port ${PORT}`);
  console.log(`📍 WebSocket server ready for real-time tracking`);
});
