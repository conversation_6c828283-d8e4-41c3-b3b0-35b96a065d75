// Enhanced color palette based on the reference designs
export const Colors = {
  // Primary brand colors
  primary: "#FF6B35", // Orange-red primary (food delivery theme)
  primaryDark: "#E55A2B", // Darker shade for pressed states
  secondary: "#4ECDC4", // Teal secondary
  accent: "#45B7D1", // Blue accent

  // Status colors
  success: "#96CEB4", // Soft green for success
  warning: "#FFEAA7", // Soft yellow for warnings
  danger: "#FF7675", // Soft red for errors
  info: "#74B9FF", // Soft blue for info

  // Neutral colors
  dark: "#2D3436", // Dark charcoal for text
  medium: "#636E72", // Medium gray for secondary text
  light: "#DDD6FE", // Light purple-gray
  lighter: "#F8F9FA", // Very light background
  white: "#FFFFFF",

  // Background gradients
  backgroundGradient: ["#FF6B35", "#F7931E"],
  cardGradient: ["#FFFFFF", "#F8F9FA"],

  // Order status colors
  statusPlaced: "#74B9FF", // Blue
  statusPreparing: "#FDCB6E", // Yellow
  statusPickedUp: "#6C5CE7", // Purple
  statusOnTheWay: "#FD79A8", // Pink
  statusDelivered: "#00B894", // Green

  // Payment method colors
  googlePay: "#4285F4",
  phonePe: "#5F259F",
  upi: "#FF6B35",
  cod: "#2D3436",

  // Additional UI colors
  shadow: "rgba(0, 0, 0, 0.1)",
  overlay: "rgba(0, 0, 0, 0.5)",
  border: "#E1E5E9",
  inputBackground: "#F8F9FA",

  // Food category colors
  pizza: "#FF6B35",
  biryani: "#FDCB6E",
  bread: "#E17055",
};
