{"name": "expo-linking", "version": "7.1.7", "description": "Create and open deep links universally", "main": "build/Linking.js", "types": "build/Linking.d.ts", "exports": {"./package.json": "./package.json", ".": {"react-server": "./build/Linking.server.js", "default": "./build/Linking.js"}}, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "test:rsc": "jest --config jest-rsc.config.js", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "expo-linking"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-linking"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/linking", "dependencies": {"expo-constants": "~17.1.7", "invariant": "^2.2.4"}, "devDependencies": {"expo-module-scripts": "^4.1.9"}, "peerDependencies": {"react": "*", "react-native": "*"}, "jest": {"preset": "expo-module-scripts"}, "gitHead": "03d3724918c94f6a46df0b48ba8ec43b995a8e96"}