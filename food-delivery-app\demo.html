<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Food Delivery App - Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
            color: #2D3436;
            min-height: 100vh;
        }

        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        .header {
            background: linear-gradient(135deg, #FF6B35 0%, #E55A2B 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
        }

        .content {
            padding: 20px;
        }

        .page {
            display: none;
        }

        .page.active {
            display: block;
        }

        .btn {
            background: linear-gradient(135deg, #FF6B35 0%, #E55A2B 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin: 10px 0;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
        }

        .btn-secondary:hover {
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin: 16px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .cart-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .cart-item:last-child {
            border-bottom: none;
        }

        .input-group {
            margin: 15px 0;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .input-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }

        .payment-option {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s;
        }

        .payment-option:hover {
            border-color: #e74c3c;
        }

        .payment-option.selected {
            border-color: #e74c3c;
            background: #fef2f2;
        }

        .status-timeline {
            margin: 20px 0;
        }

        .status-step {
            display: flex;
            align-items: center;
            margin: 20px 0;
        }

        .status-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #bdc3c7;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-weight: bold;
        }

        .status-icon.completed {
            background: #27ae60;
        }

        .status-icon.active {
            background: #e74c3c;
        }

        .status-content h4 {
            margin-bottom: 5px;
        }

        .status-content p {
            color: #7f8c8d;
            font-size: 14px;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            font-size: 18px;
            font-weight: bold;
            color: #e74c3c;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 2px solid #eee;
        }

        .home-screen {
            text-align: center;
            padding: 50px 20px;
        }

        .home-screen h1 {
            font-size: 32px;
            margin-bottom: 10px;
        }

        .home-screen p {
            font-size: 18px;
            color: #7f8c8d;
            margin-bottom: 40px;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }

            100% {
                opacity: 1;
            }
        }

        @keyframes moveAgent {
            0% {
                top: 30px;
                left: 50px;
            }

            25% {
                top: 60px;
                left: 120px;
            }

            50% {
                top: 90px;
                left: 200px;
            }

            75% {
                top: 110px;
                right: 120px;
                left: auto;
            }

            100% {
                top: 120px;
                right: 50px;
                left: auto;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h2 id="page-title">Food Delivery App</h2>
        </div>

        <!-- Home Page -->
        <div id="home" class="page active">
            <div class="home-screen">
                <h1>Food Delivery App</h1>
                <p>Order your favorite food</p>
                <button class="btn" onclick="showPage('checkout')">Start Order</button>
            </div>
        </div>

        <!-- Checkout Page -->
        <div id="checkout" class="page">
            <div class="content">
                <div class="card">
                    <h3>Order Summary</h3>
                    <div class="cart-item">
                        <div>
                            <strong>Margherita Pizza</strong><br>
                            <small>Qty: 1</small>
                        </div>
                        <span>₹299</span>
                    </div>
                    <div class="cart-item">
                        <div>
                            <strong>Chicken Biryani</strong><br>
                            <small>Qty: 2</small>
                        </div>
                        <span>₹698</span>
                    </div>
                    <div class="cart-item">
                        <div>
                            <strong>Garlic Bread</strong><br>
                            <small>Qty: 1</small>
                        </div>
                        <span>₹149</span>
                    </div>
                    <div class="total-row">
                        <span>Total: ₹1146</span>
                    </div>
                </div>

                <div class="card">
                    <h3>Delivery Address</h3>
                    <div class="input-group">
                        <label>Street Address *</label>
                        <input type="text" placeholder="Enter your street address">
                    </div>
                    <div class="input-group">
                        <label>City *</label>
                        <input type="text" placeholder="Enter your city">
                    </div>
                </div>

                <div class="card">
                    <h3>Payment Method</h3>
                    <div class="payment-option selected" onclick="selectPayment(this)">
                        <span>💳 Google Pay</span>
                        <span>○</span>
                    </div>
                    <div class="payment-option" onclick="selectPayment(this)">
                        <span>📱 PhonePe</span>
                        <span>○</span>
                    </div>
                    <div class="payment-option" onclick="selectPayment(this)">
                        <span>💰 Cash on Delivery</span>
                        <span>○</span>
                    </div>
                </div>

                <button class="btn" onclick="placeOrder()">Place Order - ₹1146</button>
            </div>
        </div>

        <!-- Order Status Page -->
        <div id="status" class="page">
            <div class="content">
                <div class="card" style="text-align: center;">
                    <h3>Order #ORD1234567890</h3>
                    <p>Estimated delivery: 25-30 mins</p>
                </div>

                <div class="card" id="delivery-partner-card" style="display: none;">
                    <h3>🚴‍♂️ Your Delivery Partner</h3>
                    <div style="display: flex; align-items: center; margin: 15px 0;">
                        <div
                            style="width: 50px; height: 50px; border-radius: 50%; background: linear-gradient(135deg, #FF6B35, #E55A2B); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; margin-right: 15px;">
                            RK</div>
                        <div style="flex: 1;">
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <strong>Rajesh Kumar</strong>
                                <span
                                    style="background: #FDCB6E; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">⭐
                                    4.8</span>
                            </div>
                            <div style="color: #636E72; font-size: 14px;">📞 +91 9876543210 • 🏍️ Bike</div>
                        </div>
                    </div>
                </div>

                <div class="card" id="delivery-map-card" style="display: none;">
                    <h3>📍 Live Tracking</h3>
                    <div id="interactive-map"
                        style="background: linear-gradient(135deg, #74B9FF, #0984e3); height: 250px; border-radius: 12px; position: relative; overflow: hidden; margin: 15px 0;">
                        <!-- Map Background with Streets -->
                        <div
                            style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(45deg, #74B9FF 25%, transparent 25%), linear-gradient(-45deg, #74B9FF 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #74B9FF 75%), linear-gradient(-45deg, transparent 75%, #74B9FF 75%); background-size: 20px 20px; opacity: 0.1;">
                        </div>

                        <!-- Restaurant Marker -->
                        <div
                            style="position: absolute; top: 30px; left: 50px; background: #FF6B35; color: white; padding: 8px; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; font-size: 18px; box-shadow: 0 2px 8px rgba(0,0,0,0.3); z-index: 10;">
                            🏪</div>
                        <div
                            style="position: absolute; top: 75px; left: 35px; background: rgba(255,255,255,0.9); padding: 4px 8px; border-radius: 4px; font-size: 12px; color: #2D3436; z-index: 10;">
                            Restaurant</div>

                        <!-- Delivery Agent Marker (Moving) -->
                        <div id="delivery-agent-marker"
                            style="position: absolute; top: 30px; left: 50px; background: #4ECDC4; color: white; padding: 8px; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; font-size: 18px; box-shadow: 0 2px 8px rgba(0,0,0,0.3); transition: all 2s ease; z-index: 10;">
                            🚴‍♂️</div>

                        <!-- Destination Marker -->
                        <div
                            style="position: absolute; top: 120px; right: 50px; background: #00B894; color: white; padding: 8px; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; font-size: 18px; box-shadow: 0 2px 8px rgba(0,0,0,0.3); z-index: 10;">
                            🏠</div>
                        <div
                            style="position: absolute; top: 165px; right: 35px; background: rgba(255,255,255,0.9); padding: 4px 8px; border-radius: 4px; font-size: 12px; color: #2D3436; z-index: 10;">
                            Your Home</div>

                        <!-- Route Line -->
                        <svg
                            style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; z-index: 5;">
                            <path d="M70 50 Q200 80 330 140" stroke="#FF6B35" stroke-width="3" fill="none"
                                stroke-dasharray="5,5" opacity="0.8" />
                        </svg>

                        <!-- Live Indicator -->
                        <div
                            style="position: absolute; top: 10px; right: 10px; background: rgba(255,255,255,0.9); padding: 6px 12px; border-radius: 20px; font-size: 12px; color: #2D3436; display: flex; align-items: center; z-index: 10;">
                            <div
                                style="width: 8px; height: 8px; background: #00B894; border-radius: 50%; margin-right: 6px; animation: pulse 2s infinite;">
                            </div>
                            LIVE
                        </div>
                    </div>
                    <div style="display: flex; align-items: center; justify-content: center; color: #636E72;">
                        <span style="margin-right: 8px;">⏱️</span>
                        <span id="map-estimate">Estimated: 15-20 mins</span>
                    </div>
                </div>

                <div class="card">
                    <h3>Order Progress</h3>
                    <div class="status-timeline">
                        <div class="status-step">
                            <div class="status-icon completed">✓</div>
                            <div class="status-content">
                                <h4>Order Placed</h4>
                                <p>Your order has been confirmed</p>
                            </div>
                        </div>
                        <div class="status-step">
                            <div class="status-icon active">🍳</div>
                            <div class="status-content">
                                <h4>Preparing</h4>
                                <p>Restaurant is preparing your food</p>
                            </div>
                        </div>
                        <div class="status-step">
                            <div class="status-icon">🚴</div>
                            <div class="status-content">
                                <h4>Picked by Delivery Partner</h4>
                                <p>Your order is with the delivery partner</p>
                            </div>
                        </div>
                        <div class="status-step">
                            <div class="status-icon">🚗</div>
                            <div class="status-content">
                                <h4>On the Way</h4>
                                <p>Your order is on the way to you</p>
                            </div>
                        </div>
                        <div class="status-step">
                            <div class="status-icon">🏠</div>
                            <div class="status-content">
                                <h4>Delivered</h4>
                                <p>Your order has been delivered</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h3>Order Details</h3>
                    <div style="display: flex; justify-content: space-between; margin: 10px 0;">
                        <span>Order ID</span>
                        <span>#ORD1234567890</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin: 10px 0;">
                        <span>Payment</span>
                        <span>Google Pay</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin: 10px 0;">
                        <span>Total Amount</span>
                        <span>₹1146</span>
                    </div>
                </div>

                <button class="btn btn-secondary" onclick="showPage('home')">Order Again</button>
            </div>
        </div>
    </div>

    <script>
        function showPage(pageId) {
            // Hide all pages
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });

            // Show selected page
            document.getElementById(pageId).classList.add('active');

            // Update header title
            const titles = {
                'home': 'Food Delivery App',
                'checkout': 'Checkout',
                'status': 'Order Status'
            };
            document.getElementById('page-title').textContent = titles[pageId];
        }

        function selectPayment(element) {
            // Remove selected class from all payment options
            document.querySelectorAll('.payment-option').forEach(option => {
                option.classList.remove('selected');
            });

            // Add selected class to clicked option
            element.classList.add('selected');
        }

        function placeOrder() {
            // Validate form fields
            const street = document.querySelector('input[placeholder="Enter your street address"]').value;
            const city = document.querySelector('input[placeholder="Enter your city"]').value;

            if (!street.trim()) {
                alert('❌ Please enter your street address');
                return;
            }

            if (!city.trim()) {
                alert('❌ Please enter your city');
                return;
            }

            // Check if payment method is selected
            const selectedPayment = document.querySelector('.payment-option.selected');
            if (!selectedPayment) {
                alert('❌ Please select a payment method');
                return;
            }

            alert('✅ Order placed successfully! 🎉');
            showPage('status');

            // Simulate status progression
            simulateStatusProgress();
        }

        function simulateStatusProgress() {
            const steps = document.querySelectorAll('.status-step');
            const partnerCard = document.getElementById('delivery-partner-card');
            const mapCard = document.getElementById('delivery-map-card');
            const mapEstimate = document.getElementById('map-estimate');

            let currentStep = 1; // Start from step 1 (preparing)

            const interval = setInterval(() => {
                if (currentStep < steps.length) {
                    const stepIcon = steps[currentStep].querySelector('.status-icon');
                    stepIcon.classList.remove('active');
                    stepIcon.classList.add('completed');
                    stepIcon.textContent = '✓';

                    // Show delivery partner and map when picked up
                    if (currentStep === 2) { // Picked up
                        partnerCard.style.display = 'block';
                        mapCard.style.display = 'block';
                        mapEstimate.textContent = 'Estimated: 15-20 mins';

                        // Start delivery agent animation
                        const agentMarker = document.getElementById('delivery-agent-marker');
                        agentMarker.style.animation = 'moveAgent 12s linear forwards';
                    }

                    // Update map estimate
                    if (currentStep === 3) { // On the way
                        mapEstimate.textContent = 'Estimated: 5-10 mins';
                    }

                    currentStep++;

                    if (currentStep < steps.length) {
                        const nextStepIcon = steps[currentStep].querySelector('.status-icon');
                        nextStepIcon.classList.add('active');
                    }
                } else {
                    clearInterval(interval);
                    mapEstimate.textContent = 'Delivered! 🎉';
                    alert('Your order has been delivered! 🍕');
                }
            }, 3000); // Update every 3 seconds for demo
        }
    </script>
</body>

</html>