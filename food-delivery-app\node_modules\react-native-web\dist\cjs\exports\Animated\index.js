"use strict";
/**
 * Copyright (c) <PERSON>.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * 
 */

'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _Animated = _interopRequireDefault(require("../../vendor/react-native/Animated/Animated"));
var _default = exports.default = _Animated.default;
module.exports = exports.default;