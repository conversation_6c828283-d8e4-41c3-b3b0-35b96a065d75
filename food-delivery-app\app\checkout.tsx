import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import { Ionicons } from "@expo/vector-icons";

import Button from "../components/Button";
import Input from "../components/Input";
import PaymentMethodCard from "../components/PaymentMethodCard";
import { Colors } from "../constants/Colors";
import { mockCartItems } from "../constants/MockData";
import { Address, PaymentMethod, PaymentInfo } from "../types";
import { OrderService } from "../services/orderService";

export default function CheckoutScreen() {
  const [address, setAddress] = useState<Address>({
    street: "",
    city: "",
    state: "",
    zipCode: "",
    landmark: "",
  });

  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>("cod");
  const [upiId, setUpiId] = useState("");
  const [errors, setErrors] = useState<Partial<Address>>({});

  const cartTotal = mockCartItems.reduce(
    (total, item) => total + item.price * item.quantity,
    0
  );

  const deliveryFee = 49;
  const tax = Math.round(cartTotal * 0.05);
  const grandTotal = cartTotal + deliveryFee + tax;

  const validateForm = (): boolean => {
    const newErrors: Partial<Address> = {};

    if (!address.street.trim()) newErrors.street = "Street address is required";
    if (!address.city.trim()) newErrors.city = "City is required";
    if (!address.state.trim()) newErrors.state = "State is required";
    if (!address.zipCode.trim()) newErrors.zipCode = "ZIP code is required";

    if (paymentMethod === "upi" && !upiId.trim()) {
      Alert.alert("Error", "Please enter UPI ID");
      return false;
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length > 0) {
      Alert.alert("Validation Error", "Please fill in all required fields");
      return false;
    }

    return true;
  };

  const handlePlaceOrder = async () => {
    if (!validateForm()) return;

    const paymentInfo: PaymentInfo = {
      method: paymentMethod,
      ...(paymentMethod === "upi" && { upiId }),
    };

    try {
      // Create order using OrderService
      const orderId = await OrderService.createOrder(
        mockCartItems,
        address,
        paymentInfo,
        grandTotal
      );

      // Start simulating order progress
      OrderService.simulateOrderProgress(orderId);

      Alert.alert(
        "Order Placed!",
        `Your order #${orderId} has been placed successfully.`,
        [
          {
            text: "Track Order",
            onPress: () => router.push(`/order-status?orderId=${orderId}`),
          },
        ]
      );
    } catch (error) {
      Alert.alert("Error", "Failed to place order. Please try again.");
    }
  };

  const PaymentMethodButton = ({
    method,
    title,
    icon,
    color,
  }: {
    method: PaymentMethod;
    title: string;
    icon: string;
    color: string;
  }) => (
    <TouchableOpacity
      style={[
        styles.paymentMethod,
        paymentMethod === method && styles.paymentMethodSelected,
      ]}
      onPress={() => setPaymentMethod(method)}
    >
      <View style={styles.paymentMethodContent}>
        <Ionicons name={icon as any} size={24} color={color} />
        <Text style={styles.paymentMethodText}>{title}</Text>
      </View>
      <View style={styles.radioButton}>
        {paymentMethod === method && (
          <View style={styles.radioButtonSelected} />
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Cart Summary */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Order Summary</Text>
          {mockCartItems.map((item) => (
            <View key={item.id} style={styles.cartItem}>
              <View style={styles.cartItemInfo}>
                <Text style={styles.cartItemName}>{item.name}</Text>
                <Text style={styles.cartItemQuantity}>
                  Qty: {item.quantity}
                </Text>
              </View>
              <Text style={styles.cartItemPrice}>
                ₹{(item.price * item.quantity).toFixed(0)}
              </Text>
            </View>
          ))}

          <View style={styles.priceBreakdown}>
            <View style={styles.priceRow}>
              <Text style={styles.priceLabel}>Subtotal</Text>
              <Text style={styles.priceValue}>₹{cartTotal}</Text>
            </View>
            <View style={styles.priceRow}>
              <Text style={styles.priceLabel}>Delivery Fee</Text>
              <Text style={styles.priceValue}>₹{deliveryFee}</Text>
            </View>
            <View style={styles.priceRow}>
              <Text style={styles.priceLabel}>Tax</Text>
              <Text style={styles.priceValue}>₹{tax}</Text>
            </View>
            <View style={[styles.priceRow, styles.totalRow]}>
              <Text style={styles.totalLabel}>Total</Text>
              <Text style={styles.totalValue}>₹{grandTotal}</Text>
            </View>
          </View>
        </View>

        {/* Delivery Address */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Delivery Address</Text>
          <Input
            label="Street Address"
            value={address.street}
            onChangeText={(text) => setAddress({ ...address, street: text })}
            error={errors.street}
            required
            placeholder="Enter your street address"
          />
          <Input
            label="City"
            value={address.city}
            onChangeText={(text) => setAddress({ ...address, city: text })}
            error={errors.city}
            required
            placeholder="Enter your city"
          />
          <View style={styles.row}>
            <View style={styles.halfWidth}>
              <Input
                label="State"
                value={address.state}
                onChangeText={(text) => setAddress({ ...address, state: text })}
                error={errors.state}
                required
                placeholder="State"
              />
            </View>
            <View style={styles.halfWidth}>
              <Input
                label="ZIP Code"
                value={address.zipCode}
                onChangeText={(text) =>
                  setAddress({ ...address, zipCode: text })
                }
                error={errors.zipCode}
                required
                placeholder="ZIP"
                keyboardType="numeric"
              />
            </View>
          </View>
          <Input
            label="Landmark (Optional)"
            value={address.landmark}
            onChangeText={(text) => setAddress({ ...address, landmark: text })}
            placeholder="Nearby landmark"
          />
        </View>

        {/* Payment Methods */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Payment Method</Text>

          <PaymentMethodCard
            method="google_pay"
            title="Google Pay"
            subtitle="Pay with your Google account"
            icon="logo-google"
            color={Colors.googlePay}
            isSelected={paymentMethod === "google_pay"}
            onSelect={() => setPaymentMethod("google_pay")}
            showBadge={true}
            badgeText="Popular"
          />

          <PaymentMethodCard
            method="phonepe"
            title="PhonePe"
            subtitle="UPI payments made easy"
            icon="card"
            color={Colors.phonePe}
            isSelected={paymentMethod === "phonepe"}
            onSelect={() => setPaymentMethod("phonepe")}
          />

          <PaymentMethodCard
            method="upi"
            title="UPI"
            subtitle="Enter your UPI ID"
            icon="qr-code"
            color={Colors.upi}
            isSelected={paymentMethod === "upi"}
            onSelect={() => setPaymentMethod("upi")}
          />

          <PaymentMethodCard
            method="cod"
            title="Cash on Delivery"
            subtitle="Pay when you receive"
            icon="cash"
            color={Colors.cod}
            isSelected={paymentMethod === "cod"}
            onSelect={() => setPaymentMethod("cod")}
            showBadge={true}
            badgeText="Safe"
          />

          {paymentMethod === "upi" && (
            <Input
              label="UPI ID"
              value={upiId}
              onChangeText={setUpiId}
              placeholder="Enter your UPI ID"
              required
              style={styles.upiInput}
            />
          )}
        </View>
      </ScrollView>

      {/* Place Order Button */}
      <View style={styles.footer}>
        <Button
          title={`Place Order - ₹${grandTotal}`}
          onPress={handlePlaceOrder}
          size="large"
          style={styles.placeOrderButton}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.lighter,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  section: {
    backgroundColor: Colors.white,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    elevation: 4,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: Colors.dark,
    marginBottom: 16,
  },
  cartItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light,
  },
  cartItemInfo: {
    flex: 1,
  },
  cartItemName: {
    fontSize: 16,
    fontWeight: "500",
    color: Colors.dark,
  },
  cartItemQuantity: {
    fontSize: 14,
    color: Colors.medium,
    marginTop: 2,
  },
  cartItemPrice: {
    fontSize: 16,
    fontWeight: "bold",
    color: Colors.dark,
  },
  priceBreakdown: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.light,
  },
  priceRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  priceLabel: {
    fontSize: 14,
    color: Colors.medium,
  },
  priceValue: {
    fontSize: 14,
    color: Colors.dark,
  },
  totalRow: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: Colors.light,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: "bold",
    color: Colors.dark,
  },
  totalValue: {
    fontSize: 18,
    fontWeight: "bold",
    color: Colors.primary,
  },
  row: {
    flexDirection: "row",
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },
  paymentMethod: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderWidth: 1,
    borderColor: Colors.light,
    borderRadius: 8,
    marginBottom: 12,
  },
  paymentMethodSelected: {
    borderColor: Colors.primary,
    backgroundColor: "#fef2f2",
  },
  paymentMethodContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  paymentMethodText: {
    fontSize: 16,
    fontWeight: "500",
    color: Colors.dark,
    marginLeft: 12,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: Colors.light,
    alignItems: "center",
    justifyContent: "center",
  },
  radioButtonSelected: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: Colors.primary,
  },
  upiInput: {
    marginTop: 12,
  },
  footer: {
    padding: 16,
    backgroundColor: Colors.white,
    borderTopWidth: 1,
    borderTopColor: Colors.light,
  },
  placeOrderButton: {
    width: "100%",
  },
});
