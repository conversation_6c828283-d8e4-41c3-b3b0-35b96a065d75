# Food Delivery App - Order Checkout + Delivery Status Tracker

A complete React Native food delivery application built with Expo, featuring order checkout and real-time delivery status tracking.

## 🚀 Features

### ✅ Completed Features

- **Home Screen**: Welcome screen with navigation to checkout
- **Checkout Page**: Complete order placement flow
  - Cart summary with mock food items
  - Delivery address form with validation
  - Multiple payment options (Google Pay, PhonePe, UPI, Cash on Delivery)
  - Order total calculation with delivery fee and tax
- **Order Status Page**: Real-time delivery tracking
  - Live status updates (Placed → Preparing → Picked Up → On the Way → Delivered)
  - Visual timeline with progress indicators
  - Automatic status progression every 12 seconds
  - Order details display
- **Backend Integration**: Order management system
  - Order creation and storage
  - Status update functionality
  - Mock data persistence using localStorage
- **Testing**: Unit tests for order service functionality

## 🛠 Tech Stack

- **Frontend**: React Native with Expo SDK 53
- **Navigation**: Expo Router v5 (file-based routing)
- **Language**: TypeScript for type safety
- **Styling**: React Native StyleSheet with custom design system
- **Backend**: Supabase integration (configured but using localStorage for demo)
- **Testing**: Jest with TypeScript support
- **Icons**: Expo Vector Icons

## 🎨 Design

The app follows the provided design references with:
- Modern color palette (Red primary, Orange secondary, Blue accent)
- Clean card-based UI components
- Responsive layout design
- Consistent typography and spacing
- Professional payment method selection
- Interactive status timeline

## 📱 How to Run

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- Expo CLI (optional, but recommended)

### Installation

1. **Clone the repository**
   ```bash
   cd food-delivery-app
   ```

2. **Install dependencies**
   ```bash
   npm install --legacy-peer-deps
   ```

3. **Start the development server**
   ```bash
   npm start
   ```

4. **Access the app**
   - **Web**: Press `w` in terminal or visit http://localhost:8081
   - **Mobile**: Scan QR code with Expo Go app
   - **Android Emulator**: Press `a` in terminal
   - **iOS Simulator**: Press `i` in terminal (macOS only)

### Testing

Run the test suite:
```bash
npm test
```

Run tests in watch mode:
```bash
npm run test:watch
```

## 🔧 Project Structure

```
food-delivery-app/
├── app/                    # Expo Router pages
│   ├── _layout.tsx        # Root layout with navigation
│   ├── index.tsx          # Home screen
│   ├── checkout.tsx       # Checkout page
│   └── order-status.tsx   # Order status tracking
├── components/            # Reusable UI components
│   ├── Button.tsx         # Custom button component
│   └── Input.tsx          # Form input component
├── constants/             # App constants
│   ├── Colors.ts          # Color palette
│   └── MockData.ts        # Mock data for testing
├── lib/                   # External service configurations
│   └── supabase.ts        # Supabase client setup
├── services/              # Business logic
│   └── orderService.ts    # Order management service
├── types/                 # TypeScript type definitions
│   └── index.ts           # App-wide type definitions
└── __tests__/             # Test files
    └── orderService.test.ts
```

## 🎯 App Flow

1. **Home Screen**: User starts their food ordering journey
2. **Checkout Page**: 
   - View cart summary (Margherita Pizza, Chicken Biryani, Garlic Bread)
   - Enter delivery address
   - Select payment method
   - Place order
3. **Order Status**: 
   - Real-time tracking with 5 status stages
   - Automatic progression simulation
   - Order details and payment information

## 🔄 Order Status Progression

The app simulates real delivery tracking with these stages:
1. **Order Placed** - Order confirmed
2. **Preparing** - Restaurant preparing food
3. **Picked by Delivery Partner** - Driver collected order
4. **On the Way** - Order in transit
5. **Delivered** - Order completed

Each stage updates automatically every 12 seconds for demonstration purposes.

## 🧪 AI Tools Used

This project was built with assistance from:
- **Claude Sonnet 4** (Augment Agent) - Primary development assistance
- **GitHub Copilot** - Code completion and suggestions
- **ChatGPT** - Problem-solving and debugging support

AI tools were used for:
- Project architecture and setup
- Component development and styling
- TypeScript type definitions
- Testing strategy and implementation
- Documentation and README creation

## 🚧 Future Enhancements

### Planned Features
- **Google Maps Integration**: Live delivery tracking with map
- **Real Supabase Backend**: Replace localStorage with actual database
- **User Authentication**: Login/signup functionality
- **Push Notifications**: Order status updates
- **Payment Gateway**: Real payment processing
- **Restaurant Management**: Multiple restaurant support

### Bonus Microservice
A Google Maps microservice is planned for:
- Live delivery agent tracking
- Route optimization
- Real-time coordinate updates
- Interactive map interface

## 📝 Notes

- The app uses localStorage for demo purposes instead of a real database
- Order status progression is simulated for demonstration
- Payment methods are mocked (no actual payment processing)
- All data is reset when the browser is refreshed

## 🤝 Contributing

This is a demo project for assignment purposes. The codebase is well-structured for future enhancements and real-world deployment.

## 📄 License

This project is created for educational/assignment purposes.

---

**Built with ❤️ using React Native + Expo**
