import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
} from "react-native";
import { SafeAreaProvider, SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";

const Colors = {
  primary: "#FF6B35",
  primaryDark: "#E55A2B",
  secondary: "#4ECDC4",
  success: "#96CEB4",
  dark: "#2D3436",
  medium: "#636E72",
  light: "#DDD6FE",
  lighter: "#F8F9FA",
  white: "#FFFFFF",
  border: "#E1E5E9",
};

type Screen = "home" | "checkout" | "status";

export default function App() {
  const [currentScreen, setCurrentScreen] = useState<Screen>("home");
  const [orderData, setOrderData] = useState({
    address: { street: "", city: "", state: "", zipCode: "" },
    paymentMethod: "cod",
    orderId: "",
  });
  const [orderStatus, setOrderStatus] = useState("placed");

  const mockCartItems = [
    { id: "1", name: "Margherita Pizza", price: 299, quantity: 1 },
    { id: "2", name: "Chicken Biryani", price: 349, quantity: 2 },
    { id: "3", name: "Garlic Bread", price: 149, quantity: 1 },
  ];

  const cartTotal = mockCartItems.reduce(
    (total, item) => total + item.price * item.quantity,
    0
  );
  const deliveryFee = 49;
  const tax = Math.round(cartTotal * 0.05);
  const grandTotal = cartTotal + deliveryFee + tax;

  const validateAndPlaceOrder = () => {
    if (!orderData.address.street.trim()) {
      Alert.alert("Error", "Please enter street address");
      return;
    }
    if (!orderData.address.city.trim()) {
      Alert.alert("Error", "Please enter city");
      return;
    }
    if (!orderData.address.state.trim()) {
      Alert.alert("Error", "Please enter state");
      return;
    }
    if (!orderData.address.zipCode.trim()) {
      Alert.alert("Error", "Please enter ZIP code");
      return;
    }

    const orderId = `ORD${Date.now()}`;
    setOrderData({ ...orderData, orderId });
    setCurrentScreen("status");

    // Simulate status progression
    setTimeout(() => setOrderStatus("preparing"), 3000);
    setTimeout(() => setOrderStatus("picked_up"), 8000);
    setTimeout(() => setOrderStatus("on_the_way"), 15000);
    setTimeout(() => setOrderStatus("delivered"), 25000);
  };

  const HomeScreen = () => (
    <View style={styles.screenContainer}>
      <View style={styles.homeContent}>
        <Text style={styles.homeTitle}>Food Delivery App</Text>
        <Text style={styles.homeSubtitle}>Order your favorite food</Text>
        <TouchableOpacity
          style={styles.primaryButton}
          onPress={() => setCurrentScreen("checkout")}
        >
          <Text style={styles.buttonText}>Start Order</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const CheckoutScreen = () => (
    <ScrollView style={styles.screenContainer}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Order Summary</Text>
        {mockCartItems.map((item) => (
          <View key={item.id} style={styles.cartItem}>
            <View>
              <Text style={styles.cartItemName}>{item.name}</Text>
              <Text style={styles.cartItemQuantity}>Qty: {item.quantity}</Text>
            </View>
            <Text style={styles.cartItemPrice}>
              ₹{item.price * item.quantity}
            </Text>
          </View>
        ))}
        <View style={styles.totalSection}>
          <View style={styles.priceRow}>
            <Text>Subtotal</Text>
            <Text>₹{cartTotal}</Text>
          </View>
          <View style={styles.priceRow}>
            <Text>Delivery Fee</Text>
            <Text>₹{deliveryFee}</Text>
          </View>
          <View style={styles.priceRow}>
            <Text>Tax</Text>
            <Text>₹{tax}</Text>
          </View>
          <View style={[styles.priceRow, styles.totalRow]}>
            <Text style={styles.totalText}>Total</Text>
            <Text style={styles.totalText}>₹{grandTotal}</Text>
          </View>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Delivery Address</Text>
        <TextInput
          style={styles.input}
          placeholder="Street Address *"
          value={orderData.address.street}
          onChangeText={(text) =>
            setOrderData({
              ...orderData,
              address: { ...orderData.address, street: text },
            })
          }
        />
        <TextInput
          style={styles.input}
          placeholder="City *"
          value={orderData.address.city}
          onChangeText={(text) =>
            setOrderData({
              ...orderData,
              address: { ...orderData.address, city: text },
            })
          }
        />
        <View style={styles.row}>
          <TextInput
            style={[styles.input, styles.halfInput]}
            placeholder="State *"
            value={orderData.address.state}
            onChangeText={(text) =>
              setOrderData({
                ...orderData,
                address: { ...orderData.address, state: text },
              })
            }
          />
          <TextInput
            style={[styles.input, styles.halfInput]}
            placeholder="ZIP Code *"
            value={orderData.address.zipCode}
            onChangeText={(text) =>
              setOrderData({
                ...orderData,
                address: { ...orderData.address, zipCode: text },
              })
            }
            keyboardType="numeric"
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Payment Method</Text>
        {[
          { id: "google_pay", title: "Google Pay", icon: "logo-google" },
          { id: "phonepe", title: "PhonePe", icon: "card" },
          { id: "upi", title: "UPI", icon: "qr-code" },
          { id: "cod", title: "Cash on Delivery", icon: "cash" },
        ].map((method) => (
          <TouchableOpacity
            key={method.id}
            style={[
              styles.paymentMethod,
              orderData.paymentMethod === method.id &&
                styles.paymentMethodSelected,
            ]}
            onPress={() =>
              setOrderData({ ...orderData, paymentMethod: method.id })
            }
          >
            <View style={styles.paymentMethodContent}>
              <Ionicons
                name={method.icon as any}
                size={24}
                color={Colors.primary}
              />
              <Text style={styles.paymentMethodText}>{method.title}</Text>
            </View>
            <View
              style={[
                styles.radioButton,
                orderData.paymentMethod === method.id &&
                  styles.radioButtonSelected,
              ]}
            >
              {orderData.paymentMethod === method.id && (
                <View style={styles.radioButtonInner} />
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>

      <TouchableOpacity
        style={styles.primaryButton}
        onPress={validateAndPlaceOrder}
      >
        <Text style={styles.buttonText}>Place Order - ₹{grandTotal}</Text>
      </TouchableOpacity>
    </ScrollView>
  );

  const StatusScreen = () => {
    const statusSteps = [
      {
        id: "placed",
        title: "Order Placed",
        description: "Your order has been confirmed",
      },
      {
        id: "preparing",
        title: "Preparing",
        description: "Restaurant is preparing your food",
      },
      {
        id: "picked_up",
        title: "Picked Up",
        description: "Order picked by delivery partner",
      },
      {
        id: "on_the_way",
        title: "On the Way",
        description: "Your order is on the way",
      },
      {
        id: "delivered",
        title: "Delivered",
        description: "Order has been delivered",
      },
    ];

    const currentIndex = statusSteps.findIndex(
      (step) => step.id === orderStatus
    );

    return (
      <ScrollView style={styles.screenContainer}>
        <View style={styles.section}>
          <Text style={styles.orderNumber}>Order #{orderData.orderId}</Text>
          <Text style={styles.estimatedTime}>
            Estimated delivery: 25-30 mins
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Order Progress</Text>
          {statusSteps.map((step, index) => (
            <View key={step.id} style={styles.statusStep}>
              <View
                style={[
                  styles.statusIcon,
                  index <= currentIndex && styles.statusIconCompleted,
                  index === currentIndex && styles.statusIconActive,
                ]}
              >
                <Ionicons
                  name={index <= currentIndex ? "checkmark" : "ellipse"}
                  size={20}
                  color={index <= currentIndex ? Colors.white : Colors.medium}
                />
              </View>
              <View style={styles.statusContent}>
                <Text
                  style={[
                    styles.statusTitle,
                    index === currentIndex && styles.statusTitleActive,
                  ]}
                >
                  {step.title}
                </Text>
                <Text style={styles.statusDescription}>{step.description}</Text>
              </View>
            </View>
          ))}
        </View>

        <TouchableOpacity
          style={styles.secondaryButton}
          onPress={() => setCurrentScreen("home")}
        >
          <Text style={styles.buttonText}>Order Again</Text>
        </TouchableOpacity>
      </ScrollView>
    );
  };

  return (
    <SafeAreaProvider>
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => setCurrentScreen("home")}>
            <Text style={styles.headerTitle}>Food Delivery App</Text>
          </TouchableOpacity>
        </View>

        {currentScreen === "home" && <HomeScreen />}
        {currentScreen === "checkout" && <CheckoutScreen />}
        {currentScreen === "status" && <StatusScreen />}
      </SafeAreaView>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.lighter,
  },
  header: {
    backgroundColor: Colors.primary,
    padding: 20,
    alignItems: "center",
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.white,
  },
  screenContainer: {
    flex: 1,
    padding: 16,
  },
  homeContent: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  homeTitle: {
    fontSize: 32,
    fontWeight: "bold",
    color: Colors.dark,
    marginBottom: 10,
    textAlign: "center",
  },
  homeSubtitle: {
    fontSize: 18,
    color: Colors.medium,
    marginBottom: 40,
    textAlign: "center",
  },
  section: {
    backgroundColor: Colors.white,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: Colors.dark,
    marginBottom: 16,
  },
  cartItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  cartItemName: {
    fontSize: 16,
    fontWeight: "500",
    color: Colors.dark,
  },
  cartItemQuantity: {
    fontSize: 14,
    color: Colors.medium,
    marginTop: 2,
  },
  cartItemPrice: {
    fontSize: 16,
    fontWeight: "bold",
    color: Colors.dark,
  },
  totalSection: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  priceRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  totalRow: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  totalText: {
    fontSize: 18,
    fontWeight: "bold",
    color: Colors.primary,
  },
  input: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: Colors.white,
    marginBottom: 12,
  },
  row: {
    flexDirection: "row",
    gap: 12,
  },
  halfInput: {
    flex: 1,
  },
  paymentMethod: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 8,
    marginBottom: 12,
  },
  paymentMethodSelected: {
    borderColor: Colors.primary,
    backgroundColor: "#FFF5F0",
  },
  paymentMethodContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  paymentMethodText: {
    fontSize: 16,
    fontWeight: "500",
    color: Colors.dark,
    marginLeft: 12,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: Colors.light,
    alignItems: "center",
    justifyContent: "center",
  },
  radioButtonSelected: {
    borderColor: Colors.primary,
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: Colors.primary,
  },
  primaryButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    alignItems: "center",
    marginVertical: 16,
  },
  secondaryButton: {
    backgroundColor: Colors.secondary,
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    alignItems: "center",
    marginVertical: 16,
  },
  buttonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: "bold",
  },
  orderNumber: {
    fontSize: 24,
    fontWeight: "bold",
    color: Colors.dark,
    textAlign: "center",
    marginBottom: 8,
  },
  estimatedTime: {
    fontSize: 16,
    color: Colors.medium,
    textAlign: "center",
  },
  statusStep: {
    flexDirection: "row",
    marginBottom: 20,
  },
  statusIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.light,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
  },
  statusIconCompleted: {
    backgroundColor: Colors.success,
  },
  statusIconActive: {
    backgroundColor: Colors.primary,
  },
  statusContent: {
    flex: 1,
    paddingTop: 8,
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.dark,
    marginBottom: 4,
  },
  statusTitleActive: {
    color: Colors.primary,
  },
  statusDescription: {
    fontSize: 14,
    color: Colors.medium,
  },
});
