{"name": "food-delivery-app", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@supabase/supabase-js": "^2.50.5", "expo": "~53.0.17", "expo-router": "^5.1.3", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "^19.1.0", "react-native": "0.79.5", "react-native-maps": "^1.24.3", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.11.1", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "ts-jest": "^29.4.0", "typescript": "~5.8.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "jsdom"}, "private": true}