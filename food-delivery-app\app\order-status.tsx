import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useLocalSearchParams } from "expo-router";
import { Ionicons } from "@expo/vector-icons";

import { Colors } from "../constants/Colors";
import { orderStatusSteps } from "../constants/MockData";
import { OrderStatus, OrderStatusInfo } from "../types";
import { OrderService } from "../services/orderService";
import DeliveryMap from "../components/DeliveryMap";

export default function OrderStatusScreen() {
  const { orderId } = useLocalSearchParams<{ orderId: string }>();
  const [currentStatus, setCurrentStatus] = useState<OrderStatus>("placed");
  const [statusSteps, setStatusSteps] =
    useState<OrderStatusInfo[]>(orderStatusSteps);
  const [refreshing, setRefreshing] = useState(false);
  const [estimatedTime, setEstimatedTime] = useState("25-30 mins");
  const [orderData, setOrderData] = useState<any>(null);
  const [deliveryPartner, setDeliveryPartner] = useState({
    name: "Rajesh Kumar",
    phone: "+91 9876543210",
    rating: 4.8,
    vehicle: "Bike",
  });

  // Fetch order data
  useEffect(() => {
    const fetchOrderData = async () => {
      if (orderId) {
        const order = await OrderService.getOrder(orderId);
        if (order) {
          setOrderData(order);
          setCurrentStatus(order.status);
          setEstimatedTime(order.estimatedDelivery || "25-30 mins");
        }
      }
    };

    fetchOrderData();
  }, [orderId]);

  // Simulate status progression
  useEffect(() => {
    const statusProgression: OrderStatus[] = [
      "placed",
      "preparing",
      "picked_up",
      "on_the_way",
      "delivered",
    ];

    let currentIndex = 0;
    const interval = setInterval(() => {
      if (currentIndex < statusProgression.length - 1) {
        currentIndex++;
        setCurrentStatus(statusProgression[currentIndex]);
        updateStatusSteps(statusProgression[currentIndex]);

        // Update estimated time
        const remainingSteps = statusProgression.length - 1 - currentIndex;
        const timeEstimates = [
          "20-25 mins",
          "15-20 mins",
          "10-15 mins",
          "5-10 mins",
          "Delivered!",
        ];
        setEstimatedTime(timeEstimates[currentIndex]);
      } else {
        clearInterval(interval);
      }
    }, 12000); // Update every 12 seconds for demo

    return () => clearInterval(interval);
  }, []);

  const updateStatusSteps = (newStatus: OrderStatus) => {
    const statusOrder: OrderStatus[] = [
      "placed",
      "preparing",
      "picked_up",
      "on_the_way",
      "delivered",
    ];
    const currentIndex = statusOrder.indexOf(newStatus);

    setStatusSteps((prevSteps) =>
      prevSteps.map((step, index) => ({
        ...step,
        isCompleted: index <= currentIndex,
        timestamp:
          index === currentIndex
            ? new Date().toLocaleTimeString()
            : step.timestamp,
      }))
    );
  };

  const onRefresh = () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const getStatusColor = (status: OrderStatus): string => {
    switch (status) {
      case "placed":
        return Colors.statusPlaced;
      case "preparing":
        return Colors.statusPreparing;
      case "picked_up":
        return Colors.statusPickedUp;
      case "on_the_way":
        return Colors.statusOnTheWay;
      case "delivered":
        return Colors.statusDelivered;
      default:
        return Colors.medium;
    }
  };

  const getStatusIcon = (status: OrderStatus): string => {
    switch (status) {
      case "placed":
        return "checkmark-circle";
      case "preparing":
        return "restaurant";
      case "picked_up":
        return "bicycle";
      case "on_the_way":
        return "car";
      case "delivered":
        return "home";
      default:
        return "ellipse";
    }
  };

  const StatusStep = ({
    step,
    index,
  }: {
    step: OrderStatusInfo;
    index: number;
  }) => {
    const isActive = step.status === currentStatus;
    const isCompleted = step.isCompleted;
    const isLast = index === statusSteps.length - 1;

    return (
      <View style={styles.statusStep}>
        <View style={styles.statusStepLeft}>
          <View
            style={[
              styles.statusIcon,
              isCompleted && styles.statusIconCompleted,
              isActive && styles.statusIconActive,
            ]}
          >
            <Ionicons
              name={getStatusIcon(step.status) as any}
              size={20}
              color={isCompleted || isActive ? Colors.white : Colors.medium}
            />
          </View>
          {!isLast && (
            <View
              style={[
                styles.statusLine,
                isCompleted && styles.statusLineCompleted,
              ]}
            />
          )}
        </View>

        <View style={styles.statusContent}>
          <Text
            style={[styles.statusTitle, isActive && styles.statusTitleActive]}
          >
            {step.title}
          </Text>
          <Text style={styles.statusDescription}>{step.description}</Text>
          {step.timestamp && (
            <Text style={styles.statusTime}>{step.timestamp}</Text>
          )}
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Order Header */}
        <View style={styles.header}>
          <Text style={styles.orderNumber}>Order #{orderId}</Text>
          <Text style={styles.estimatedTime}>
            Estimated delivery: {estimatedTime}
          </Text>
        </View>

        {/* Current Status Card */}
        <View style={styles.currentStatusCard}>
          <View style={styles.currentStatusHeader}>
            <Ionicons
              name={getStatusIcon(currentStatus) as any}
              size={32}
              color={getStatusColor(currentStatus)}
            />
            <View style={styles.currentStatusText}>
              <Text style={styles.currentStatusTitle}>
                {statusSteps.find((s) => s.status === currentStatus)?.title}
              </Text>
              <Text style={styles.currentStatusDescription}>
                {
                  statusSteps.find((s) => s.status === currentStatus)
                    ?.description
                }
              </Text>
            </View>
          </View>

          {/* Delivery Partner Info */}
          {(currentStatus === "picked_up" ||
            currentStatus === "on_the_way") && (
            <View style={styles.deliveryPartnerInfo}>
              <View style={styles.partnerHeader}>
                <Ionicons
                  name="person-circle"
                  size={24}
                  color={Colors.primary}
                />
                <Text style={styles.partnerName}>{deliveryPartner.name}</Text>
                <View style={styles.ratingContainer}>
                  <Ionicons name="star" size={16} color={Colors.warning} />
                  <Text style={styles.rating}>{deliveryPartner.rating}</Text>
                </View>
              </View>
              <Text style={styles.partnerDetails}>
                📞 {deliveryPartner.phone} • 🏍️ {deliveryPartner.vehicle}
              </Text>
            </View>
          )}
        </View>

        {/* Delivery Map */}
        {(currentStatus === "picked_up" || currentStatus === "on_the_way") && (
          <DeliveryMap
            orderStatus={currentStatus}
            restaurantLocation={{
              latitude: 28.6139,
              longitude: 77.209,
              name: "Delicious Bites Restaurant",
            }}
            deliveryLocation={{
              latitude: 28.6289,
              longitude: 77.2065,
              address: orderData?.address?.street || "Your Address",
            }}
          />
        )}

        {/* Status Timeline */}
        <View style={styles.timelineCard}>
          <Text style={styles.timelineTitle}>Order Progress</Text>
          {statusSteps.map((step, index) => (
            <StatusStep key={step.status} step={step} index={index} />
          ))}
        </View>

        {/* Order Details */}
        <View style={styles.orderDetailsCard}>
          <Text style={styles.orderDetailsTitle}>Order Details</Text>
          <View style={styles.orderDetailRow}>
            <Text style={styles.orderDetailLabel}>Order ID</Text>
            <Text style={styles.orderDetailValue}>#{orderId}</Text>
          </View>
          <View style={styles.orderDetailRow}>
            <Text style={styles.orderDetailLabel}>Payment</Text>
            <Text style={styles.orderDetailValue}>
              {orderData?.payment?.method === "cod"
                ? "Cash on Delivery"
                : orderData?.payment?.method === "google_pay"
                ? "Google Pay"
                : orderData?.payment?.method === "phonepe"
                ? "PhonePe"
                : orderData?.payment?.method === "upi"
                ? `UPI (${orderData?.payment?.upiId})`
                : "Cash on Delivery"}
            </Text>
          </View>
          <View style={styles.orderDetailRow}>
            <Text style={styles.orderDetailLabel}>Total Amount</Text>
            <Text style={styles.orderDetailValue}>
              ₹{orderData?.total || 1146}
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  header: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    alignItems: "center",
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  orderNumber: {
    fontSize: 24,
    fontWeight: "bold",
    color: Colors.dark,
    marginBottom: 8,
  },
  estimatedTime: {
    fontSize: 16,
    color: Colors.medium,
  },
  currentStatusCard: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  currentStatusHeader: {
    flexDirection: "row",
    alignItems: "center",
  },
  currentStatusText: {
    marginLeft: 16,
    flex: 1,
  },
  currentStatusTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.dark,
    marginBottom: 4,
  },
  currentStatusDescription: {
    fontSize: 16,
    color: Colors.medium,
  },
  timelineCard: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  timelineTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: Colors.dark,
    marginBottom: 20,
  },
  statusStep: {
    flexDirection: "row",
    marginBottom: 20,
  },
  statusStepLeft: {
    alignItems: "center",
    marginRight: 16,
  },
  statusIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.light,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 8,
  },
  statusIconCompleted: {
    backgroundColor: Colors.success,
  },
  statusIconActive: {
    backgroundColor: Colors.primary,
  },
  statusLine: {
    width: 2,
    flex: 1,
    backgroundColor: Colors.light,
  },
  statusLineCompleted: {
    backgroundColor: Colors.success,
  },
  statusContent: {
    flex: 1,
    paddingTop: 8,
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.dark,
    marginBottom: 4,
  },
  statusTitleActive: {
    color: Colors.primary,
  },
  statusDescription: {
    fontSize: 14,
    color: Colors.medium,
    marginBottom: 4,
  },
  statusTime: {
    fontSize: 12,
    color: Colors.medium,
    fontStyle: "italic",
  },
  orderDetailsCard: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 20,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  orderDetailsTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: Colors.dark,
    marginBottom: 16,
  },
  orderDetailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  orderDetailLabel: {
    fontSize: 16,
    color: Colors.medium,
  },
  orderDetailValue: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.dark,
  },
  deliveryPartnerInfo: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  partnerHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  partnerName: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.dark,
    marginLeft: 8,
    flex: 1,
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.warning,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  rating: {
    fontSize: 12,
    fontWeight: "600",
    color: Colors.white,
    marginLeft: 4,
  },
  partnerDetails: {
    fontSize: 14,
    color: Colors.medium,
    marginLeft: 32,
  },
});
