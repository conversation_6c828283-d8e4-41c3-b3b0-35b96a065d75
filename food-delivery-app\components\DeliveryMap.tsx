import React, { useState, useEffect } from "react";
import { View, Text, StyleSheet, Dimensions, Platform } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "../constants/Colors";

// For web, we'll show a placeholder. For mobile, we'd use react-native-maps
const isWeb = Platform.OS === "web";

interface DeliveryMapProps {
  orderStatus: string;
  restaurantLocation: {
    latitude: number;
    longitude: number;
    name: string;
  };
  deliveryLocation: {
    latitude: number;
    longitude: number;
    address: string;
  };
}

const { width, height } = Dimensions.get("window");

export default function DeliveryMap({
  orderStatus,
  restaurantLocation,
  deliveryLocation,
}: DeliveryMapProps) {
  const [deliveryAgentLocation, setDeliveryAgentLocation] =
    useState(restaurantLocation);
  const [routeCoordinates, setRouteCoordinates] = useState([
    restaurantLocation,
    deliveryLocation,
  ]);

  // Simulate delivery agent movement
  useEffect(() => {
    if (orderStatus === "on_the_way" || orderStatus === "picked_up") {
      const interval = setInterval(() => {
        setDeliveryAgentLocation((prev) => {
          const latDiff = deliveryLocation.latitude - prev.latitude;
          const lngDiff = deliveryLocation.longitude - prev.longitude;

          // Move 10% closer to destination each update
          const newLat = prev.latitude + latDiff * 0.1;
          const newLng = prev.longitude + lngDiff * 0.1;

          return {
            latitude: newLat,
            longitude: newLng,
          };
        });
      }, 2000); // Update every 2 seconds

      return () => clearInterval(interval);
    }
  }, [orderStatus, deliveryLocation]);

  const getMapRegion = () => {
    const latitudes = [
      restaurantLocation.latitude,
      deliveryLocation.latitude,
      deliveryAgentLocation.latitude,
    ];
    const longitudes = [
      restaurantLocation.longitude,
      deliveryLocation.longitude,
      deliveryAgentLocation.longitude,
    ];

    const minLat = Math.min(...latitudes);
    const maxLat = Math.max(...latitudes);
    const minLng = Math.min(...longitudes);
    const maxLng = Math.max(...longitudes);

    const latDelta = (maxLat - minLat) * 1.5; // Add padding
    const lngDelta = (maxLng - minLng) * 1.5;

    return {
      latitude: (minLat + maxLat) / 2,
      longitude: (minLng + maxLng) / 2,
      latitudeDelta: Math.max(latDelta, 0.01),
      longitudeDelta: Math.max(lngDelta, 0.01),
    };
  };

  const getDeliveryAgentIcon = () => {
    switch (orderStatus) {
      case "preparing":
        return "restaurant";
      case "picked_up":
      case "on_the_way":
        return "bicycle";
      case "delivered":
        return "checkmark-circle";
      default:
        return "location";
    }
  };

  // Web placeholder component
  const WebMapPlaceholder = () => (
    <View style={styles.mapPlaceholder}>
      <View style={styles.mapBackground}>
        {/* Restaurant Marker */}
        <View style={[styles.marker, styles.restaurantMarker, { top: 30, left: 50 }]}>
          <Ionicons name="restaurant" size={20} color={Colors.white} />
        </View>
        <Text style={[styles.markerLabel, { top: 75, left: 35 }]}>Restaurant</Text>

        {/* Delivery Agent Marker */}
        {(orderStatus === 'picked_up' || orderStatus === 'on_the_way') && (
          <View style={[styles.marker, styles.agentMarker, {
            top: orderStatus === 'on_the_way' ? 90 : 60,
            left: orderStatus === 'on_the_way' ? 200 : 120
          }]}>
            <Ionicons name="bicycle" size={20} color={Colors.white} />
          </View>
        )}

        {/* Destination Marker */}
        <View style={[styles.marker, styles.destinationMarker, { top: 120, right: 50 }]}>
          <Ionicons name="home" size={20} color={Colors.white} />
        </View>
        <Text style={[styles.markerLabel, { top: 165, right: 35 }]}>Your Home</Text>

        {/* Live Indicator */}
        <View style={styles.liveIndicator}>
          <View style={styles.liveDot} />
          <Text style={styles.liveText}>LIVE</Text>
        </View>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Live Tracking</Text>
        <Text style={styles.subtitle}>
          {orderStatus === "preparing" && "Preparing your order..."}
          {orderStatus === "picked_up" && "Order picked up by delivery partner"}
          {orderStatus === "on_the_way" && "On the way to you"}
          {orderStatus === "delivered" && "Order delivered!"}
        </Text>
      </View>

      <WebMapPlaceholder />
      </MapView>

      <View style={styles.footer}>
        <View style={styles.estimateContainer}>
          <Ionicons name="time" size={16} color={Colors.medium} />
          <Text style={styles.estimateText}>
            {orderStatus === "preparing" && "Estimated: 20-25 mins"}
            {orderStatus === "picked_up" && "Estimated: 15-20 mins"}
            {orderStatus === "on_the_way" && "Estimated: 5-10 mins"}
            {orderStatus === "delivered" && "Delivered!"}
          </Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.white,
    borderRadius: 16,
    overflow: "hidden",
    elevation: 4,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    marginBottom: 20,
  },
  header: {
    padding: 16,
    backgroundColor: Colors.lighter,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    color: Colors.dark,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: Colors.medium,
  },
  mapPlaceholder: {
    width: "100%",
    height: 250,
    position: "relative",
  },
  mapBackground: {
    width: "100%",
    height: "100%",
    backgroundColor: "#74B9FF",
    position: "relative",
  },
  marker: {
    position: "absolute",
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 3,
    borderColor: Colors.white,
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  restaurantMarker: {
    backgroundColor: Colors.primary,
  },
  agentMarker: {
    backgroundColor: Colors.info,
  },
  destinationMarker: {
    backgroundColor: Colors.success,
  },
  markerLabel: {
    position: "absolute",
    backgroundColor: "rgba(255,255,255,0.9)",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    fontSize: 12,
    color: Colors.dark,
  },
  liveIndicator: {
    position: "absolute",
    top: 10,
    right: 10,
    backgroundColor: "rgba(255,255,255,0.9)",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    flexDirection: "row",
    alignItems: "center",
  },
  liveDot: {
    width: 8,
    height: 8,
    backgroundColor: Colors.success,
    borderRadius: 4,
    marginRight: 6,
  },
  liveText: {
    fontSize: 12,
    fontWeight: "bold",
    color: Colors.dark,
  },
  footer: {
    padding: 16,
    backgroundColor: Colors.lighter,
  },
  estimateContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  estimateText: {
    fontSize: 14,
    fontWeight: "500",
    color: Colors.medium,
    marginLeft: 8,
  },
});
